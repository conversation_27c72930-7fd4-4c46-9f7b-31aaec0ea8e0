# Power Pages Configuration Best Practices

## Accessing Site Settings and Environment Variables

### ✅ RECOMMENDED APPROACH: Meta Tags with Liquid Templates

After extensive testing and troubleshooting, the **meta tag approach** is the most reliable method for accessing Power Pages site settings and environment variables.

#### Implementation Pattern:

```html
<!-- Meta tags for configuration - PROVEN WORKING METHOD -->
<meta name="azure-function-url" content="{{ settings['AzureFunctionUrl'] | default: '' }}">
<meta name="azure-function-key" content="{{ settings['AzureFunctionKey'] | default: '' }}">
<meta name="application-name" content="{{ settings['ApplicationName'] | default: '' }}">

<script>
  // Extract configuration using the proven working method
  function getConfigFromWorkingMethods() {
    const functionUrl = document.querySelector('meta[name="azure-function-url"]')?.content;
    const functionKey = document.querySelector('meta[name="azure-function-key"]')?.content;
    const applicationName = document.querySelector('meta[name="application-name"]')?.content;
    
    return {
      functionUrl: functionUrl || null,
      azureFunctionKey: functionKey || null,
      applicationName: applicationName || null
    };
  }
  
  // Set up configuration
  window.appConfig = getConfigFromWorkingMethods();
</script>
```

### Why This Approach Works

1. **Liquid Templates Process Correctly**: Meta tag content is processed by Power Pages Liquid engine
2. **No Span Wrapping Issues**: Unlike JavaScript objects, meta tags don't get wrapped in `<span>` elements
3. **Simple Extraction**: Standard `document.querySelector()` works reliably
4. **Console Verified**: Confirmed working through browser console testing

### ❌ PROBLEMATIC APPROACHES

#### Direct JavaScript Object Assignment
```html
<!-- PROBLEMATIC - Gets wrapped in spans -->
<script>
  window.appConfig = {
    functionUrl: {{ settings['AzureFunctionUrl'] | json }}, // Becomes span-wrapped
    azureFunctionKey: {{ settings['AzureFunctionKey'] | json }}
  };
</script>
```

**Issues:**
- Liquid templates get wrapped in `<span data-editorblocktype="Liquid">` elements
- Values become inaccessible to JavaScript
- Parsing errors and "missing" configuration

#### Hidden Div/Input Approaches
```html
<!-- UNRELIABLE -->
<div id="config" data-url="{{ settings['AzureFunctionUrl'] }}"></div>
<input type="hidden" name="functionUrl" value="{{ settings['AzureFunctionUrl'] }}">
```

**Issues:**
- Inconsistent processing
- Values often empty or undefined
- Not reliable across different Power Pages configurations

### Site Settings Configuration

Ensure your Power Pages site settings are configured with:
- **Source**: Environment Variable (recommended)
- **Name**: Exact match to Liquid template reference
- **Environment Variable**: Linked to your Dataverse environment variables

### Testing and Validation

Always test configuration extraction in browser console:
```javascript
// Test meta tag extraction
console.log("URL:", document.querySelector('meta[name="azure-function-url"]')?.content);
console.log("Key:", document.querySelector('meta[name="azure-function-key"]')?.content);
console.log("App:", document.querySelector('meta[name="application-name"]')?.content);
```

### Implementation Notes

- Use `default: ''` in Liquid templates to prevent errors
- Check for null/empty values before using configuration
- Log configuration status for debugging (without exposing sensitive values)
- This pattern works consistently across all Power Pages implementations

---

**Status**: Verified working approach as of 2025-01-31
**Files Using This Pattern**: send-invitation.html
