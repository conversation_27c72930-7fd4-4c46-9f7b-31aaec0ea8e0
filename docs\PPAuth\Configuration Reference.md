# Configuration Reference

Complete reference for all configuration settings in the PowerPages Custom Authentication system.

## 🔧 **Azure Function App Settings**

### **Core Configuration** ⚠️ **Required**

```json
{
  "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=...",
  "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated"
}
```

### **Entra External ID** ⚠️ **Required**

```json
{
  "EntraExternalID:TenantId": "********-1234-1234-1234-********9012",
  "EntraExternalID:ClientId": "********-4321-4321-4321-************",
  "EntraExternalID:ClientSecret": "your-client-secret-or-keyvault-reference",
  "EntraExternalID:DefaultDomain": "yourtenant.onmicrosoft.com"
}
```

**Key Vault References** (Production):
```json
{
  "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/ClientSecret/)"
}
```

### **Email Service (SendGrid)** ⚠️ **Required**

```json
{
  "SendGrid:ApiKey": "SG.your-sendgrid-api-key",
  "SendGrid:FromEmail": "<EMAIL>",
  "PasswordReset:BaseUrl": "https://your-powerpage.powerappsportals.com"
}
```

**Key Vault Reference** (Production):
```json
{
  "SendGrid:ApiKey": "@Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/SendGridApiKey/)"
}
```

**Setup Requirements**:
- SendGrid account with verified domain or single sender
- API key with Mail Send permissions
- See [SendGrid Setup Guide](../../SENDGRID_SETUP_GUIDE.md) for detailed configuration

### **Storage Configuration** ⚠️ **Required**

```json
{
  "Storage:ConnectionString": "DefaultEndpointsProtocol=https;AccountName=..."
}
```

**Note**: No fallback configuration. `Storage:ConnectionString` must be explicitly configured in local.settings.json.

### **Password History Settings** (Optional)

```json
{
  "PasswordHistory:MaxCount": "12",
  "PasswordHistory:WorkFactor": "12"
}
```

**Defaults**:
- `MaxCount`: 12 passwords
- `WorkFactor`: 12 (4,096 BCrypt iterations)

### **Rate Limiting** (Optional)

```json
{
  "RateLimit:MaxRequestsPerMinute": "60"
}
```

**Default**: 60 requests per minute per client

### **Application Settings** (Optional)

```json
{
  "ApplicationName": "Your Application Name",
  "GraphApiEndpoint": "https://graph.microsoft.com/v1.0"
}
```

### **Monitoring** (Optional)

```json
{
  "APPINSIGHTS_INSTRUMENTATIONKEY": "your-instrumentation-key",
  "APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=...",
  "ApplicationInsightsAgent_EXTENSION_VERSION": "~3"
}
```

## 🌐 **Power Pages Site Settings**

### **Core Settings** ⚠️ **Required**

```
AzureFunctionUrl = https://your-function-app.azurewebsites.net/api
ApplicationName = Your Application Name
MSALClientId = your-entra-external-id-client-id
MSALTenantId = your-tenant-id
```

### **Security Settings** (Production)

```
AzureFunctionKey = your-function-key
```

**Note**: Only required when using Function authorization level.

### **Authentication Settings** ⚠️ **Required**

```
Authentication/Registration/Enabled = false
Authentication/Registration/RequiresConfirmation = false
Authentication/Registration/RequiresInvitation = false
Authentication/Registration/RegistrationPath = /Custom-User-Registration

Authentication/PasswordReset/Enabled = false
Authentication/PasswordReset/ResetPasswordPath = /Custom-Password-Reset
Authentication/PasswordChange/Enabled = false
Authentication/PasswordChange/ChangePasswordPath = /Custom-Password-Reset

Authentication/LogoutPath = /.auth/logout
```

### **Optional Settings**

```
ApplicationDescription = Secure customer portal for account management
SupportEmail = <EMAIL>
PrivacyPolicyUrl = https://yourcompany.com/privacy
TermsOfServiceUrl = https://yourcompany.com/terms
CompanyName = Your Company Name
EntraTenantDomain = yourtenant.onmicrosoft.com
AuthProvider = EntraExternalID
```

## 📄 **host.json Configuration**

### **Basic Configuration**

```json
{
  "version": "2.0",
  "logging": {
    "applicationInsights": {
      "samplingSettings": {
        "isEnabled": true,
        "excludedTypes": "Request"
      }
    },
    "logLevel": {
      "default": "Information",
      "Function": "Information",
      "PasswordHistoryValidator": "Information"
    }
  }
}
```

### **HTTP Configuration**

```json
{
  "extensions": {
    "http": {
      "routePrefix": "api",
      "maxOutstandingRequests": 200,
      "maxConcurrentRequests": 100,
      "dynamicThrottlesEnabled": true
    }
  }
}
```

### **CORS Configuration**

**Development**:
```json
{
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": ["*"],
        "allowedMethods": ["GET", "POST", "OPTIONS"],
        "allowedHeaders": ["Content-Type", "Authorization", "x-functions-key", "X-Requested-With", "X-Client-Version"]
      }
    }
  }
}
```

**Production** ⚠️ **Required**:
```json
{
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": [
          "https://your-production-domain.powerappsportals.com",
          "https://your-staging-domain.powerappsportals.com"
        ],
        "allowedMethods": ["GET", "POST", "OPTIONS"],
        "allowedHeaders": ["Content-Type", "Authorization", "x-functions-key", "X-Requested-With", "X-Client-Version"]
      }
    }
  }
}
```

## 🔐 **Security Configuration**

### **Function Authorization Levels**

**Development/Testing**:
```csharp
[HttpTrigger(AuthorizationLevel.Anonymous, ...)]
```

**Production** ⚠️ **Required**:
```csharp
[HttpTrigger(AuthorizationLevel.Function, ...)]
```

### **Azure Key Vault Setup**

**Create Key Vault**:
```bash
az keyvault create --name your-auth-keyvault --resource-group your-rg --location eastus
```

**Store Secrets**:
```bash
az keyvault secret set --vault-name your-auth-keyvault --name "ClientSecret" --value "your-client-secret"
az keyvault secret set --vault-name your-auth-keyvault --name "SMTP2GOApiKey" --value "api-your-smtp2go-key"
```

**Function App Managed Identity**:
```bash
az functionapp identity assign --name your-function-app --resource-group your-rg
az keyvault set-policy --name your-auth-keyvault --object-id MANAGED_IDENTITY_ID --secret-permissions get
```

## 🌍 **Environment-Specific Configurations**

### **Development**

```json
{
  "AzureWebJobsStorage": "UseDevelopmentStorage=true",
  "StorageConnectionString": "UseDevelopmentStorage=true",
  "EntraExternalID:ClientSecret": "dev-client-secret",
  "SMTP2GOApiKey": "api-dev-key",
  "ResetPasswordBaseUrl": "https://localhost:3000"
}
```

### **Staging**

```json
{
  "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=stagingstorage...",
  "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(SecretUri=https://staging-vault.vault.azure.net/secrets/ClientSecret/)",
  "SMTP2GOApiKey": "@Microsoft.KeyVault(SecretUri=https://staging-vault.vault.azure.net/secrets/SMTP2GOApiKey/)",
  "ResetPasswordBaseUrl": "https://staging-site.powerappsportals.com"
}
```

### **Production**

```json
{
  "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=prodstorage...",
  "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(SecretUri=https://prod-vault.vault.azure.net/secrets/ClientSecret/)",
  "SMTP2GOApiKey": "@Microsoft.KeyVault(SecretUri=https://prod-vault.vault.azure.net/secrets/SMTP2GOApiKey/)",
  "ResetPasswordBaseUrl": "https://production-site.powerappsportals.com"
}
```

## ✅ **Configuration Validation**

### **Required Settings Check**

Use the UtilityService to validate configuration:
```bash
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=config-check&code=YOUR_KEY"
```

### **Configuration Checklist**

**Azure Function App**:
- [ ] `AzureWebJobsStorage` configured
- [ ] `FUNCTIONS_WORKER_RUNTIME` = "dotnet-isolated"
- [ ] Entra External ID settings complete
- [ ] SMTP2GO settings configured
- [ ] Authorization level set appropriately
- [ ] CORS configured for production domains

**Power Pages**:
- [ ] `AzureFunctionUrl` points to correct Function App
- [ ] `ApplicationName` matches across all configurations
- [ ] Authentication settings disable built-in registration/reset
- [ ] Custom page paths configured correctly

**Security**:
- [ ] Secrets moved to Key Vault (production)
- [ ] Managed Identity enabled and configured
- [ ] Function keys generated (if using Function authorization)
- [ ] CORS restricted to specific domains (production)

## 🔧 **Configuration Management**

### **Best Practices**

1. **Environment Separation**: Use different configurations for dev/staging/prod
2. **Secret Management**: Always use Key Vault for production secrets
3. **Documentation**: Keep configuration changes documented
4. **Validation**: Test configuration changes in staging first
5. **Backup**: Keep backup of working configurations

### **Configuration Deployment**

**Via Azure CLI**:
```bash
az functionapp config appsettings set \
  --name your-function-app \
  --resource-group your-rg \
  --settings @settings.json
```

**Via ARM Template**:
```json
{
  "type": "Microsoft.Web/sites/config",
  "apiVersion": "2021-02-01",
  "name": "[concat(parameters('functionAppName'), '/appsettings')]",
  "properties": {
    "AzureWebJobsStorage": "[parameters('storageConnectionString')]",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated"
  }
}
```

## 🚨 **Common Configuration Issues**

### **Missing Required Settings**
- Check UtilityService config-check endpoint
- Review Application Insights for startup errors
- Verify all required settings are present

### **Key Vault Access Issues**
- Ensure Managed Identity is enabled
- Verify Key Vault access policies
- Check Key Vault reference format

### **CORS Problems**
- Verify domain spelling in allowedOrigins
- Ensure HTTPS is used in production
- Check for trailing slashes in domain names

### **Email Configuration Issues**
- Verify SMTP2GO API key format (starts with "api-")
- Check domain authentication in SMTP2GO
- Ensure sender email is verified
