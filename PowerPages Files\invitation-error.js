// Invitation Error Page JavaScript
// Handles error display, user interactions, and support requests

document.addEventListener('DOMContentLoaded', function() {
    console.log('Invitation Error Page loaded');
    
    // Initialize error page
    initializeErrorPage();
    
    // Set up event listeners
    setupEventListeners();
    
    // Load and display error details
    loadErrorDetails();
});

function initializeErrorPage() {
    console.log('Initializing invitation error page...');
    
    // Validate configuration
    if (!window.appConfig) {
        console.error('App configuration not found');
        return;
    }
    
    // Log configuration for debugging
    console.log('Error page configuration:', {
        applicationName: window.appConfig.applicationName,
        supportEmail: window.appConfig.supportEmail
    });
}

function setupEventListeners() {
    // Show/hide technical details
    const showDetailsBtn = document.getElementById('showDetails');
    const errorDetails = document.getElementById('errorDetails');
    
    if (showDetailsBtn && errorDetails) {
        showDetailsBtn.addEventListener('click', function() {
            const isHidden = errorDetails.classList.contains('hidden');
            
            if (isHidden) {
                errorDetails.classList.remove('hidden');
                showDetailsBtn.innerHTML = '<i class="fas fa-eye-slash"></i> Hide Technical Details';
            } else {
                errorDetails.classList.add('hidden');
                showDetailsBtn.innerHTML = '<i class="fas fa-info-circle"></i> Show Technical Details';
            }
        });
    }
    
    // Request new invitation modal
    const requestBtn = document.getElementById('requestNewInvitation');
    const modal = document.getElementById('requestInvitationModal');
    const closeBtn = document.getElementById('closeModal');
    
    if (requestBtn && modal) {
        requestBtn.addEventListener('click', function() {
            modal.classList.remove('hidden');
        });
    }
    
    if (closeBtn && modal) {
        closeBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });
        
        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });
    }
    
    // Escape key to close modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal && !modal.classList.contains('hidden')) {
            modal.classList.add('hidden');
        }
    });
}

function loadErrorDetails() {
    try {
        // Try to get error details from session storage (set by registration.js)
        const errorData = sessionStorage.getItem('invitationError');
        
        if (errorData) {
            const error = JSON.parse(errorData);
            displayErrorDetails(error);
            
            // Clear the error from session storage
            sessionStorage.removeItem('invitationError');
        } else {
            // Check URL parameters for error information
            const urlParams = new URLSearchParams(window.location.search);
            const errorMessage = urlParams.get('error');
            const errorCode = urlParams.get('code');
            
            if (errorMessage || errorCode) {
                displayErrorDetails({
                    message: errorMessage || 'Unknown error',
                    code: errorCode,
                    source: 'url-parameters',
                    timestamp: new Date().toISOString()
                });
            }
        }
    } catch (error) {
        console.error('Error loading error details:', error);
    }
}

function displayErrorDetails(errorData) {
    console.log('Displaying error details:', errorData);
    
    // Update main error message if available
    if (errorData.message) {
        const errorMessage = document.getElementById('errorMessage');
        if (errorMessage) {
            const messageText = errorMessage.querySelector('p');
            if (messageText) {
                messageText.textContent = errorData.message;
            }
        }
    }
    
    // Populate technical details
    const errorDetailsContent = document.getElementById('errorDetailsContent');
    if (errorDetailsContent && errorData) {
        const details = {
            'Error Message': errorData.message || 'Not specified',
            'Error Code': errorData.code || 'Not specified',
            'Source': errorData.source || 'Unknown',
            'Timestamp': errorData.timestamp || 'Not specified',
            'Application': window.appConfig?.applicationName || 'Not specified'
        };
        
        let detailsText = '';
        for (const [key, value] of Object.entries(details)) {
            detailsText += `${key}: ${value}\n`;
        }
        
        errorDetailsContent.textContent = detailsText;
    }
    
    // Update page title based on error type
    updatePageTitle(errorData);
}

function updatePageTitle(errorData) {
    if (!errorData || !errorData.message) return;
    
    const message = errorData.message.toLowerCase();
    const errorMessage = document.querySelector('.error-message');
    const titleElement = errorMessage?.querySelector('h3');
    
    if (!titleElement) return;
    
    // Customize title based on error type
    if (message.includes('expired')) {
        titleElement.textContent = 'Invitation Expired';
        const description = errorMessage.querySelector('p');
        if (description) {
            description.textContent = 'Your invitation link has expired. Please request a new invitation from your administrator.';
        }
    } else if (message.includes('used') || message.includes('already')) {
        titleElement.textContent = 'Invitation Already Used';
        const description = errorMessage.querySelector('p');
        if (description) {
            description.textContent = 'This invitation has already been used to create an account. If you already have an account, please try logging in.';
        }
    } else if (message.includes('invalid') || message.includes('not found')) {
        titleElement.textContent = 'Invalid Invitation';
        const description = errorMessage.querySelector('p');
        if (description) {
            description.textContent = 'The invitation link appears to be invalid or corrupted. Please check the link and try again, or request a new invitation.';
        }
    }
}

// Utility function to sanitize user input
function sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
}

// Function to log user actions for analytics
function logUserAction(action, details = {}) {
    try {
        console.log('User action:', action, details);
        
        // You could send this to your analytics service
        // Example: analytics.track(action, details);
    } catch (error) {
        console.error('Error logging user action:', error);
    }
}

// Track page view
logUserAction('invitation_error_page_view', {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    referrer: document.referrer
});

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeErrorPage,
        setupEventListeners,
        loadErrorDetails,
        displayErrorDetails,
        updatePageTitle,
        sanitizeInput,
        logUserAction
    };
}
