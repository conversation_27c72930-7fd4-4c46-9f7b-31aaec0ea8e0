Password management within Power Pages, using Entra External ID for primary authentication. Enforces a 12-password history compliance by intercepting standard password operations (registration, reset) with a custom backend built on Azure Functions.

Setting a configuration value for password history is not a default feature of Entra External ID, so we are intercepting operations at key points. We are required to create custom Registration / Password Reset scenarios due to our limited access with password operations in Entra External ID. Graph API enables us to perform necessary Entra External ID actions while also using our Azure function for password history.

---

### Components & Roles

- **Power Pages (Frontend):** Provides the custom UI for registration, password reset, and login. JavaScript handles client-side validation and API calls to the Azure Functions backend.
- **Entra External ID (Identity Provider):** Handles standard user sign-in and session management.
- **Azure Functions (Backend Logic):** Manages all custom password logic.
  - Handles new user registration and validation.
  - Performs password history checks against Azure Blob Storage.
  - Executes user creation and password updates via the Microsoft Graph API.
- **Azure Blob Storage (Data Storage):** Stores the 12-password history for each user in an encrypted JSON format, with data isolation between Power Pages applications.
- **Microsoft Graph API (User Management):** Used by the Azure Functions to create and update user accounts and passwords in the Entra External ID tenant.
- **SMTP2GO (Email Service):** Integrated for sending emails such as password reset

---

### **User Authentication Workflows**

#### **1. Standard Login**

- A user navigates to the Power Pages application and is redirected to the standard Entra External ID login page.
- Entra External ID validates the user's credentials.
- Upon success, the user is redirected back to Power Pages with an authenticated session.

#### **2. Custom Registration**

- A new user fills out the custom registration form on Power Pages.
- The form submits the user's details to the Azure Function API.
- The Azure Function:
  1. Validates the password complexity.
  2. Creates the user in Entra External ID via the Microsoft Graph API.
  3. Stores the initial password hash in the Azure Blob Storage history file.

#### **3. Password Reset (Logged-In User)**

- An authenticated user accesses the "Change Password" page.
- The user provides their current and new passwords.
- Azure Function:
  1. Verifies the current password.
  2. Validates the new password against the 12-entry history in Azure Blob Storage.
  3. If valid, updates the password in Entra External ID via the Graph API.
  4. Adds the new password hash to the history file in Blob Storage.
- The user is logged out of all sessions and prompted to login with the new password.

#### **4. Forgot Password (Logged-Out User)**

- A user enters their email into the public "Forgot Password" form.
- The Azure Function triggers SMTP2GO to send a reset link.
- The user clicks the link and is taken to a custom Power Pages form to enter a new password.
- The Azure Function validates the new password against the history and updates the user's account via the Graph API.
- A confirmation email is sent upon successful reset and the use is redirected to login with their new password.

---

### **Security**

- **12-Password History Compliance:** Prevents password reuse across all change/reset operations. Bcrypt hashing is used
- **Application Isolation:** Password history is stored in a way that segregates user data per Power Pages application.
- **Backend:** API keys and connection strings for backend services are managed securely in Azure Key Vault.
- **Session Invalidation:** User sessions are automatically terminated after a password reset to prevent unauthorized access.

---

### Notes\*\*

- Migrated from the Graph API beta to v1.0 for production stability.
- Current Azure Functions Authorization Level temporarily set to Anonymous for debugging registration flows and must be secured before QA / Production deployment

## Select Documentation References

### Entra External ID

- [Microsoft Entra External ID Overview](https://learn.microsoft.com/en-us/entra/external-id/external-identities-overview)
- [Configure Entra External ID for customer identity and access management](https://learn.microsoft.com/en-us/entra/external-id/customers/overview-customers-ciam)
- [Set up user flows in Entra External ID](https://learn.microsoft.com/en-us/entra/external-id/customers/how-to-user-flow-sign-up-sign-in-customers)

### Power Pages Authentication

- [Power Pages site settings for authentication](https://learn.microsoft.com/en-us/power-pages/configure/configure-site-settings)

### Azure Functions

- [Azure Functions overview](https://learn.microsoft.com/en-us/azure/azure-functions/functions-overview)
- [Azure Functions HTTP triggers and bindings](https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-http-webhook)
- [Secure Azure Functions](https://learn.microsoft.com/en-us/azure/azure-functions/security-concepts)

### Microsoft Graph API

- [Microsoft Graph overview](https://learn.microsoft.com/en-us/graph/overview)
- [Microsoft Graph v1.0 endpoint reference](https://learn.microsoft.com/en-us/graph/api/overview?view=graph-rest-1.0)
- [User resource type](https://learn.microsoft.com/en-us/graph/api/resources/user?view=graph-rest-1.0)

### Azure Blob Storage

- [Azure Blob Storage overview](https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blobs-overview)
- [Azure Blob Storage security recommendations](https://learn.microsoft.com/en-us/azure/storage/blobs/security-recommendations)
