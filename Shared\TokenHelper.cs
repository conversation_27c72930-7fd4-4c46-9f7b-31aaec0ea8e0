using System.Security.Cryptography;

namespace PasswordHistoryValidator.Shared;

public static class TokenHelper
{

    public static string GenerateVerificationCode()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var randomNumber = BitConverter.ToUInt32(bytes, 0);
        var code = (randomNumber % 900000) + 100000;
        return code.ToString();
    }
}