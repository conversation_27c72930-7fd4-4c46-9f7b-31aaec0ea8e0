# SendGrid Configuration Guide

## Overview

This guide covers all SendGrid configuration requirements for the invitation-based user management system.

## Required SendGrid Setup

### 1. SendGrid Account Setup

1. **Create SendGrid Account**

   - Go to https://sendgrid.com/
   - Sign up for an account (free tier available)
   - Verify your email address

2. **Domain Authentication (Recommended)**

   - Go to Settings → Sender Authentication → Domain Authentication
   - Add your domain (e.g., yourdomain.com)
   - Follow DNS configuration instructions
   - Verify domain ownership

3. **Single Sender Verification (Alternative)**
   - Go to Settings → Sender Authentication → Single Sender Verification
   - Add the email address you'll send from
   - Verify the sender email

### 2. API Key Creation

1. **Generate API Key**

   - Go to Settings → API Keys
   - Click "Create API Key"
   - Choose "Restricted Access"
   - Grant the following permissions:
     - Mail Send: Full Access
     - Template Engine: Read Access
     - Stats: Read Access (optional)
   - Save the API key securely

2. **API Key Format**
   ```
   SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   ```

### 3. Email Templates

You need to create two dynamic email templates in SendGrid:

#### Template 1: User Invitation Email

1. **Create Template**

   - Go to Email API → Dynamic Templates
   - Click "Create a Dynamic Template"
   - Name: "User Invitation"
   - Click "Add Version"

2. **Template Content**

   - Use the SendGrid Design Editor
   - Copy the content from `Services/Templates/UserInvitation.html`
   - Set up the following dynamic variables:
     - `{{firstName}}`
     - `{{invitedBy}}`
     - `{{applicationName}}`
     - `{{registrationLink}}`
     - `{{verificationCode}}`
     - `{{expirationDate}}`
     - `{{correlationId}}`

3. **Subject Line**

   ```
   You're invited to join {{applicationName}}
   ```

4. **Template Settings**
   - Enable "Generate Plain-Text"
   - Test with sample data
   - Save and note the Template ID

#### Template 2: Account Created Notification

1. **Create Template**

   - Go to Email API → Dynamic Templates
   - Click "Create a Dynamic Template"
   - Name: "Account Created"
   - Click "Add Version"

2. **Template Content**

   - Copy the content from `Services/Templates/AccountCreated.html`
   - Set up the following dynamic variables:
     - `{{firstName}}`
     - `{{email}}`
     - `{{applicationName}}`
     - `{{loginUrl}}`
     - `{{correlationId}}`

3. **Subject Line**

   ```
   Welcome to {{applicationName}} - Account Created
   ```

4. **Template Settings**
   - Enable "Generate Plain-Text"
   - Test with sample data
   - Save and note the Template ID

### 4. Template HTML Content

#### User Invitation Template

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>You're Invited!</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        background-color: #0078d4;
        color: white;
        padding: 20px;
        text-align: center;
        border-radius: 8px 8px 0 0;
      }
      .content {
        background-color: #f9f9f9;
        padding: 30px;
        border-radius: 0 0 8px 8px;
      }
      .button {
        display: inline-block;
        background-color: #0078d4;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 5px;
        font-weight: bold;
        margin: 20px 0;
      }
      .verification-code {
        background-color: #e8f4fd;
        border: 2px solid #0078d4;
        padding: 15px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        letter-spacing: 2px;
        margin: 20px 0;
        border-radius: 5px;
      }
      .footer {
        text-align: center;
        margin-top: 30px;
        font-size: 12px;
        color: #666;
      }
      .security-note {
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        color: #856404;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>You're Invited!</h1>
    </div>
    <div class="content">
      <p>Hello {{firstName}},</p>
      <p>
        You've been invited by <strong>{{invitedBy}}</strong> to join
        <strong>{{applicationName}}</strong>.
      </p>
      <p>
        To complete your registration, click the button below and use the
        verification code provided:
      </p>
      <div class="verification-code">{{verificationCode}}</div>
      <div style="text-align: center;">
        <a href="{{registrationLink}}" class="button">Complete Registration</a>
      </div>
      <div class="security-note">
        <strong>Security Information:</strong>
        <ul>
          <li>This invitation expires on {{expirationDate}}</li>
          <li>The verification code is required for registration</li>
          <li>This invitation can only be used once</li>
          <li>
            If you didn't expect this invitation, please ignore this email
          </li>
        </ul>
      </div>
      <p>
        If you have any questions, please contact the person who invited you.
      </p>
      <p>Best regards,<br />The {{applicationName}} Team</p>
    </div>
    <div class="footer">
      <p>Reference ID: {{correlationId}}</p>
      <p>This is an automated message. Please do not reply to this email.</p>
    </div>
  </body>
</html>
```

#### Account Created Template

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Welcome to {{applicationName}}</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        background-color: #28a745;
        color: white;
        padding: 20px;
        text-align: center;
        border-radius: 8px 8px 0 0;
      }
      .content {
        background-color: #f9f9f9;
        padding: 30px;
        border-radius: 0 0 8px 8px;
      }
      .button {
        display: inline-block;
        background-color: #28a745;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 5px;
        font-weight: bold;
        margin: 20px 0;
      }
      .account-info {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
      }
      .footer {
        text-align: center;
        margin-top: 30px;
        font-size: 12px;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>Welcome to {{applicationName}}!</h1>
    </div>
    <div class="content">
      <p>Hello {{firstName}},</p>
      <p>
        Your account has been successfully created! You can now access
        {{applicationName}} using your credentials.
      </p>
      <div class="account-info">
        <strong>Account Details:</strong>
        <ul>
          <li>Email: {{email}}</li>
          <li>Application: {{applicationName}}</li>
          <li>Account Status: Active</li>
        </ul>
      </div>
      <div style="text-align: center;">
        <a href="{{loginUrl}}" class="button">Login to Your Account</a>
      </div>
      <p><strong>Next Steps:</strong></p>
      <ul>
        <li>Keep your login credentials secure</li>
        <li>Bookmark the login page for easy access</li>
        <li>Contact support if you need assistance</li>
      </ul>
      <p>Thank you for joining {{applicationName}}!</p>
      <p>Best regards,<br />The {{applicationName}} Team</p>
    </div>
    <div class="footer">
      <p>Reference ID: {{correlationId}}</p>
      <p>This is an automated message. Please do not reply to this email.</p>
    </div>
  </body>
</html>
```

### 5. Testing Templates

#### Test User Invitation Template

Use this test data in SendGrid:

```json
{
  "firstName": "John",
  "invitedBy": "<EMAIL>",
  "applicationName": "MyApp Portal",
  "registrationLink": "https://yoursite.com/register?token=test&code=ABC123",
  "verificationCode": "ABC123",
  "expirationDate": "March 10, 2025",
  "correlationId": "test-correlation-id"
}
```

#### Test Account Created Template

```json
{
  "firstName": "John",
  "email": "<EMAIL>",
  "applicationName": "MyApp Portal",
  "loginUrl": "https://yoursite.com/login",
  "correlationId": "test-correlation-id"
}
```

## Configuration Requirements

### Environment Variables Needed

Add these to your `local.settings.json` for development:

```json
{
  "Values": {
    "SendGrid__ApiKey": "SG.your_sendgrid_api_key_here",
    "SendGrid__FromEmail": "<EMAIL>",
    "SendGrid__FromName": "Your Application Name",
    "SendGrid__UserInvitationTemplateId": "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "SendGrid__AccountCreatedTemplateId": "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "SendGrid__BypassMode": "false"
  }
}
```

### Azure Function App Settings

In Azure portal, add these application settings:

| Setting Name                         | Value                    | Description                     |
| ------------------------------------ | ------------------------ | ------------------------------- |
| `SendGrid__ApiKey`                   | `SG.xxx...`              | Your SendGrid API key           |
| `SendGrid__FromEmail`                | `<EMAIL>` | Verified sender email           |
| `SendGrid__FromName`                 | `Your App Name`          | Display name for emails         |
| `SendGrid__UserInvitationTemplateId` | `d-xxx...`               | Invitation template ID          |
| `SendGrid__AccountCreatedTemplateId` | `d-xxx...`               | Welcome template ID             |
| `SendGrid__BypassMode`               | `false`                  | Set to `true` to disable emails |

## Email Testing

### Development Testing

1. **Enable Bypass Mode**

   ```json
   "SendGrid__BypassMode": "true"
   ```

   - Emails won't be sent but operations complete successfully
   - Useful for development and testing

2. **Test with Real Emails**
   ```json
   "SendGrid__BypassMode": "false"
   ```
   - Use your own email address for testing
   - Verify templates render correctly

### Production Validation

1. **Send Test Invitation**

   ```bash
   curl -X POST "https://yourapp.azurewebsites.net/api/InvitationService?operation=invite-user" \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "firstName": "Test",
       "lastName": "User",
       "applicationName": "TestApp",
       "invitedBy": "<EMAIL>"
     }'
   ```

2. **Check Email Delivery**
   - Verify email arrives promptly
   - Check spam/junk folders
   - Validate all dynamic content renders correctly

## Monitoring and Analytics

### SendGrid Dashboard

Monitor these metrics:

- **Delivery Rate**: Should be >95%
- **Open Rate**: Typically 20-30%
- **Click Rate**: Varies by content
- **Bounce Rate**: Should be <5%
- **Spam Reports**: Should be minimal

### Azure Function Logs

Monitor for:

- SendGrid API errors
- Template rendering issues
- Rate limiting responses
- Authentication failures

## Troubleshooting

### Common Issues

#### "Invalid API Key"

- Check API key format and permissions
- Ensure key hasn't been revoked
- Verify key has Mail Send permissions

#### "Template Not Found"

- Verify template ID format (starts with `d-`)
- Check template is published and active
- Ensure correct template ID in configuration

#### "From Email Not Verified"

- Complete domain authentication or single sender verification
- Check sender email matches verified address
- Wait for verification process to complete

#### "Emails Not Being Delivered"

- Check SendGrid activity logs
- Verify recipient email addresses
- Check spam/junk folders
- Review bounce and block lists

### Debug Steps

1. **Enable Detailed Logging**

   ```json
   "Logging__LogLevel__PasswordHistoryValidator.Services.EmailService": "Debug"
   ```

2. **Test SendGrid Connection**

   ```csharp
   // Test API connectivity
   var client = new SendGridClient(apiKey);
   var response = await client.RequestAsync(
       method: SendGridClient.Method.GET,
       urlPath: "user/account"
   );
   ```

3. **Validate Template Data**
   - Ensure all template variables are provided
   - Check for null or empty values
   - Verify data types match template expectations

## Security Considerations

### API Key Security

- Store API keys securely in Azure Key Vault
- Rotate keys regularly
- Use minimum required permissions
- Monitor API key usage

### Email Security

- Use authenticated domains
- Implement SPF, DKIM, and DMARC records
- Monitor for phishing attempts
- Include security warnings in emails

### Template Security

- Sanitize all dynamic content
- Avoid exposing sensitive data
- Use HTTPS links only
- Include correlation IDs for tracking

## Maintenance

### Regular Tasks

- Monitor delivery rates
- Update templates as needed
- Review and rotate API keys
- Check domain authentication status

### Monthly Reviews

- Analyze email performance metrics
- Review bounce and complaint rates
- Update sender reputation
- Test template rendering across email clients

For SendGrid-specific support, visit: https://support.sendgrid.com/
