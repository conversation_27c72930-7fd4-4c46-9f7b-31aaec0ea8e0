.forgot-password-container {
    max-width: 500px;
}

.forgot-password-container h2::after {
    width: 60px;
    height: 2px;
    background-color: var(--primary-red);
    margin: 1rem auto;
}

#resetButton {
    background-color: var(--primary-red);
    color: var(--color-white);
    border: 2px solid var(--primary-red);
}

#resetButton:hover {
    background-color: var(--primary-red-dark);
    border-color: var(--primary-red-dark);
}

.text-center.mt-3 .btn-outline-secondary:hover {
    background-color: var(--gray-light);
    border-color: var(--gray-medium);
    color: var(--gray-dark);
    text-decoration: none;
}

.text-center.mt-3 a:not(.btn):hover {
    color: var(--primary-red-dark);
    text-decoration: underline;
}

/* Authentication Testing Section */
.auth-testing-section {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    border-top: 1px solid var(--gray-border);
}

.auth-testing-section h3 {
    text-align: center;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-red);
    font-size: 1.25rem;
}

.auth-links-container {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.auth-link-group {
    text-align: center;
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-border);
    border-radius: var(--radius-md);
    background-color: var(--gray-light);
    min-width: 150px;
}

.auth-link-group .btn {
    margin-bottom: var(--spacing-xs);
    min-width: 120px;
}

.auth-description {
    margin: 0;
    color: var(--gray-medium);
    font-size: 0.85rem;
}

@media (max-width: 768px) {
    .forgot-password-container {
        max-width: 95%;
    }

    .auth-links-container {
        flex-direction: column;
        align-items: center;
    }

    .auth-link-group {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 480px) {
    .forgot-password-container h2::after {
        width: 40px;
        margin: 0.75rem auto;
    }
}
