# UtilityService Documentation

## Overview

The UtilityService is a dedicated Azure Function that provides operational monitoring, maintenance, and administrative capabilities for the PowerPagesCustomAuth system. It serves as the "control center" for system health monitoring, configuration validation, and automated maintenance tasks.

## Core Functions

### 1. Health Monitoring (`?operation=health`)

**Purpose**: Comprehensive system health check across all services and dependencies.

**What it checks**:
- Password History Service health
- Rate Limiting Service functionality  
- Configuration Service validation
- Blob Storage connectivity
- Current configuration values

**Response includes**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "3.0.0-simplified",
  "services": {
    "passwordHistoryService": "healthy",
    "rateLimitingService": "healthy", 
    "configurationService": "healthy",
    "blobStorage": "healthy"
  },
  "configuration": {
    "maxHistoryCount": 12,
    "workFactor": 12,
    "rateLimitPerMinute": 60
  }
}
```

**Use cases**:
- Production monitoring dashboards
- Automated health checks
- Troubleshooting system issues
- Pre-deployment validation

### 2. Configuration Validation (`?operation=config-check`)

**Purpose**: Validates all required configuration settings and provides security recommendations.

**What it validates**:
- **Required Settings**:
  - `AzureWebJobsStorage` - Azure Functions storage
  - `FUNCTIONS_WORKER_RUNTIME` - Runtime configuration
  - `EntraExternalID:ClientId` - Entra External ID app registration
  - `EntraExternalID:ClientSecret` - App registration secret
  - `EntraExternalID:TenantId` - Tenant identifier

- **Recommended Settings**:
  - `PasswordHistory:MaxCount` - Number of previous passwords to track
  - `PasswordHistory:WorkFactor` - BCrypt hashing strength
  - `RateLimit:MaxRequestsPerMinute` - API rate limiting

**Security Recommendations Provided**:

1. **BCrypt Work Factor Security**:
   - Warns if work factor < 10 (too weak)
   - Warns if work factor > 15 (performance impact)
   - Optimal range: 10-15

2. **Password History Security**:
   - Recommends minimum 5 previous passwords
   - Warns if > 25 (performance impact)
   - Optimal range: 5-25

3. **Rate Limiting Security**:
   - Warns if > 300 requests/minute (abuse potential)
   - Helps prevent brute force attacks

**Response includes**:
```json
{
  "isValid": true,
  "missingSettings": [],
  "timestamp": "2024-01-01T12:00:00Z",
  "recommendations": [
    "Consider increasing BCrypt work factor to at least 10 for better security"
  ]
}
```

### 3. Token Cleanup (`?operation=cleanup-tokens`)

**Purpose**: Automated maintenance to remove expired password reset tokens from blob storage.

**What it does**:
- Scans all reset tokens in blob storage
- Identifies expired or used tokens
- Removes obsolete tokens to prevent storage bloat
- Provides cleanup statistics

**Response includes**:
```json
{
  "tokensProcessed": 150,
  "tokensRemoved": 45,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Benefits**:
- Prevents unlimited storage growth
- Maintains system performance
- Removes security-sensitive expired data
- Provides audit trail of cleanup operations

### 4. System Statistics (`?operation=stats`)

**Purpose**: Provides operational insights into system usage and data volumes.

**Metrics provided**:
- Total password history entries
- Total reset tokens (active + expired)
- Active reset tokens count
- Expired tokens count
- Timestamp of analysis

**Response includes**:
```json
{
  "passwordHistoryEntries": 1250,
  "totalResetTokens": 89,
  "activeResetTokens": 12,
  "expiredTokens": 77,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Use cases**:
- Capacity planning
- Usage pattern analysis
- Performance monitoring
- Security auditing

### 5. Graph API Testing (`?operation=test-graph`)

**Purpose**: Validates Microsoft Graph API connectivity and configuration.

**What it tests**:
- Entra External ID configuration presence
- Graph API authentication
- Basic Graph API operations
- Service principal permissions

**Response includes**:
```json
{
  "configurationStatus": {
    "tenantIdConfigured": true,
    "clientIdConfigured": true,
    "clientSecretConfigured": true,
    "tenantIdValue": "12345678...",
    "clientIdValue": "87654321..."
  },
  "graphApiTest": "SUCCESS",
  "error": null
}
```

## Security Features

### 1. Configuration Security Validation
- Ensures all security-critical settings are configured
- Validates setting ranges to prevent weak security
- Provides specific recommendations for hardening

### 2. Automated Token Cleanup
- Removes expired security tokens automatically
- Prevents token accumulation that could be exploited
- Maintains clean security posture

### 3. Rate Limiting Monitoring
- Validates rate limiting configuration
- Warns about settings that could allow abuse
- Helps prevent brute force attacks

### 4. Secure Credential Handling
- Masks sensitive values in responses
- Only shows partial credential identifiers
- Logs security events appropriately

## Operational Benefits

### For Development
- Quick validation of local configuration
- Easy troubleshooting of service issues
- Immediate feedback on security settings

### For Production
- Automated health monitoring
- Proactive maintenance operations
- Security compliance validation
- Performance optimization insights

## Usage Examples

### Health Check
```
GET https://your-function-app.azurewebsites.net/api/UtilityService?operation=health
```

### Configuration Validation
```
GET https://your-function-app.azurewebsites.net/api/UtilityService?operation=config-check
```

### Token Cleanup (Maintenance)
```
GET https://your-function-app.azurewebsites.net/api/UtilityService?operation=cleanup-tokens
```

### System Statistics
```
GET https://your-function-app.azurewebsites.net/api/UtilityService?operation=stats
```

### Graph API Test
```
GET https://your-function-app.azurewebsites.net/api/UtilityService?operation=test-graph
```

## Recommendations for Enhancement

### Current State: ✅ Solid Foundation
The current UtilityService provides excellent operational capabilities and is sufficient for most production needs.

### Potential Future Enhancements

1. **Monitoring Integration**
   - Azure Application Insights integration
   - Custom metrics export
   - Alerting on health check failures

2. **Advanced Security Features**
   - Security audit logging
   - Compliance reporting
   - Vulnerability scanning integration

3. **Performance Monitoring**
   - Response time tracking
   - Resource utilization metrics
   - Performance trend analysis

4. **Automated Maintenance**
   - Scheduled cleanup operations
   - Automatic configuration optimization
   - Self-healing capabilities

## Conclusion

The UtilityService is a valuable operational component that provides:
- ✅ Comprehensive health monitoring
- ✅ Security configuration validation
- ✅ Automated maintenance operations
- ✅ Operational insights and statistics
- ✅ Production-ready monitoring capabilities

**Recommendation**: Keep and utilize the UtilityService - it provides essential operational capabilities that become increasingly valuable as your system scales and moves to production.
