using System.ComponentModel.DataAnnotations;

namespace PasswordHistoryValidator;

public class AuthRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;

    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Department { get; set; }
    public string? UserRole { get; set; }
    public string? RegistrationSource { get; set; } = "PowerPages";
    public bool RememberMe { get; set; } = false;
    public string? UserId { get; set; }
    public string? CurrentPassword { get; set; }
    public bool UpdateHistory { get; set; } = false;
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

public class PasswordRequest
{
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string? Email { get; set; }
    public string? UserId { get; set; }

    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;

    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string? NewPassword { get; set; }
    public string? CurrentPassword { get; set; }
    public string? Token { get; set; }
    public string? VerificationCode { get; set; }
    public bool UpdateHistory { get; set; } = true;
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

public class InvitationRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [MaxLength(256, ErrorMessage = "Email cannot exceed 256 characters")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "First name is required")]
    [MaxLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
    public string FirstName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Last name is required")]
    [MaxLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
    public string LastName { get; set; } = string.Empty;

    [MaxLength(100, ErrorMessage = "Application name cannot exceed 100 characters")]
    public string ApplicationName { get; set; } = string.Empty;

    // Optional fields for invitation completion
    public string? Token { get; set; }
    public string? VerificationCode { get; set; }
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string? Password { get; set; }
    
    // Flag to force overwrite existing invitation
    public bool ForceOverwrite { get; set; } = false;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

public class ResetTokenData
{
    public string Email { get; set; } = string.Empty;
    public string ApplicationId { get; set; } = string.Empty;
    public string Token { get; set; } = string.Empty;
    public string VerificationCode { get; set; } = string.Empty;
    public DateTime CreatedUtc { get; set; }
    public DateTime ExpiresUtc { get; set; }
    public bool Used { get; set; }
    public DateTime? UsedUtc { get; set; }
}

public class InvitationTokenData
{
    public string Email { get; set; } = string.Empty;
    public string ApplicationId { get; set; } = string.Empty;
    public string Token { get; set; } = string.Empty;
    public string VerificationCode { get; set; } = string.Empty;
    public DateTime CreatedUtc { get; set; }
    public DateTime ExpiresUtc { get; set; }
    public bool Used { get; set; }
    public DateTime? UsedUtc { get; set; }
    
    // Token supersession tracking
    public bool IsSuperseded { get; set; } = false;
    public DateTime? SupersededUtc { get; set; }
    public string SupersededReason { get; set; } = string.Empty;
}

public class TokenValidationRequest
{
    [Required(ErrorMessage = "Token is required")]
    public string Token { get; set; } = string.Empty;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

