.registration-container {
    max-width: 650px;
}

#registrationForm .input-group .form-control {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

#registrationForm .input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

#registrationForm .input-group .btn:hover {
    background-color: var(--primary-red);
    border-color: var(--primary-red);
    color: var(--color-white);
}

#registrationForm .form-text.text-success {
    color: #28a745 !important;
}

#registrationForm .form-check-label a:hover {
    color: var(--primary-red-dark);
    text-decoration: underline;
}

#submitButton {
    margin-top: 1rem;
}

#messageContent {
    padding: 1rem;
    border-radius: var(--radius-md);
    border-left: 4px solid;
}

.text-center.mt-3 {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-border);
}

/* Authentication Testing Section */
.auth-testing-section {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    border-top: 1px solid var(--gray-border);
}

.auth-testing-section h3 {
    text-align: center;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-red);
    font-size: 1.25rem;
}

.auth-links-container {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.auth-link-group {
    text-align: center;
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-border);
    border-radius: var(--radius-md);
    background-color: var(--gray-light);
    min-width: 150px;
}

.auth-link-group .btn {
    margin-bottom: var(--spacing-xs);
    min-width: 120px;
}

.auth-description {
    margin: 0;
    color: var(--gray-medium);
    font-size: 0.85rem;
}

@media (max-width: 768px) {
    .registration-container {
        max-width: 95%;
    }

    .auth-links-container {
        flex-direction: column;
        align-items: center;
    }

    .auth-link-group {
        width: 100%;
        max-width: 200px;
    }
}
