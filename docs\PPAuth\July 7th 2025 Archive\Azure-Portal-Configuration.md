# Azure Portal Configuration for Password Reset

## Required Azure Function App Settings

To fix the password reset functionality in Azure Portal, you need to configure these application settings in your Azure Function App:

### 1. Navigate to Configuration
1. Go to Azure Portal → Your Function App
2. Click **Configuration** in the left menu
3. Click **Application settings** tab

### 2. Required Settings

Add these application settings (replace with your actual values):

```
EntraExternalID:TenantId = your-actual-tenant-id
EntraExternalID:ClientId = your-actual-client-id  
EntraExternalID:ClientSecret = your-actual-client-secret
```

### 3. Storage Configuration

Ensure you have either:
```
AzureWebJobsStorage = DefaultEndpointsProtocol=https;AccountName=...
```
OR
```
StorageConnectionString = DefaultEndpointsProtocol=https;AccountName=...
```

### 4. Optional Settings (with defaults)
```
PasswordHistory:MaxCount = 12
PasswordHistory:WorkFactor = 12
RateLimit:MaxRequestsPerMinute = 60
ApplicationName = Power Pages Application
```

### 5. SendGrid Configuration (for email)
```
SendGrid:ApiKey = your-sendgrid-api-key
SendGrid:FromEmail = <EMAIL>
SendGrid:FromName = Power Pages Application
```

## How to Get Entra External ID Values

1. **Azure Portal** → **Microsoft Entra ID** → **App registrations**
2. Find your application registration
3. Copy **Application (client) ID** and **Directory (tenant) ID**
4. Go to **Certificates & secrets** → Create new **Client secret**

## Testing the Configuration

After updating settings:
1. **Restart** the Function App
2. Test the password reset functionality
3. Check Function App **Logs** for any errors

## Common Issues

- **500 Internal Server Error**: Missing required configuration values
- **Empty JSON Response**: Function failing to start due to config validation
- **CORS Errors**: Check that your Power Pages domain is allowed

## Verification

You can verify configuration by calling:
```
GET https://your-function-app.azurewebsites.net/api/UtilityService?operation=health
```

This should return configuration status and any missing settings.
