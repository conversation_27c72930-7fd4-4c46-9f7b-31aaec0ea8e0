# SendGrid Invitation Template Setup

## Overview

This document provides instructions for setting up the User Invitation dynamic template in SendGrid to work with the PowerPagesCustomAuth invitation system.

## Template Variables

The invitation email template receives the following dynamic variables:

```json
{
  "invitationLink": "https://yoursite.com/registration?code=ABC123",
  "verificationCode": "ABC123",
  "applicationName": "Your Application Name",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "fullName": "John Doe"
}
```

## SendGrid Setup Steps

### 1. Create Dynamic Template

1. Log into your SendGrid account
2. Go to **Email API** → **Dynamic Templates**
3. Click **Create a Dynamic Template**
4. Name: "User Invitation"
5. Click **Add Version**

### 2. Template Content

Copy the content from `Services/Templates/UserInvitation.html` into the SendGrid template editor.

**Key Features:**

- Personalized greeting using `{{firstName}}`
- Prominent verification code display
- Direct link to registration page
- Osler branding (red, white, black colors)
- Security information and expiration notice

### 3. Subject Line

```
You're invited to join {{applicationName}}
```

### 4. Template Variables to Configure

In the SendGrid template editor, ensure these variables are properly mapped:

- `{{firstName}}` - User's first name
- `{{applicationName}}` - Application name
- `{{verificationCode}}` - 6-digit verification code
- `{{invitationLink}}` - Direct link to registration page
- `{{fullName}}` - Full name (firstName + lastName)
- `{{lastName}}` - User's last name (if needed separately)

### 5. Test Data

Use this test data in SendGrid to preview the template:

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "John Doe",
  "applicationName": "Osler Portal",
  "verificationCode": "ABC123",
  "invitationLink": "https://yoursite.com/registration?code=ABC123"
}
```

## Configuration

After creating the templates, update your `local.settings.json`:

```json
{
  "SendGrid": {
    "ApiKey": "REPLACE_WITH_YOUR_SENDGRID_API_KEY",
    "FromEmail": "<EMAIL>",
    "PasswordResetTemplateId": "d-dc912d5057e84d46a0b1e402ededde15",
    "PasswordChangedTemplateId": "d-1ca6c61c4cbf46c7ab74a5086b5af8ac",
    "UserInvitationTemplateId": "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "AccountCreatedTemplateId": "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  }
}
```

**Required Templates:**

- **UserInvitationTemplateId**: For sending invitation emails with verification codes
- **AccountCreatedTemplateId**: For welcome emails after successful registration

## Important Notes

- The invitation link now points directly to `/registration?code=` instead of `/accept-invitation`
- Users enter the verification code on the registration page
- Template uses inline CSS for email client compatibility
- Follows Osler branding guidelines (red #dc2626, white, black)
- Includes security information about code expiration and single-use

## Workflow

1. Admin uses send-invitation page to invite user
2. System generates verification code and invitation token
3. SendGrid sends personalized email using this template
4. User clicks link → goes to registration page
5. User enters verification code from email
6. Registration validates code and creates account
