# SMTP2GO Email Implementation

Implementing direct SMTP2GO email functionality for password management system.

## **Credential Storage Options**

### **Option 1: Azure Key Vault (Recommended for Production)**

**Best for**: Production environments requiring maximum security

**Configuration**:

```json
{
  "SMTP2GOApiKey": "@Microsoft.KeyVault(SecretUri=https://[your-vault].vault.azure.net/secrets/SMTP2GOApiKey/)",
  "SMTP2GOFromEmail": "<EMAIL>",
  "SMTP2GOFromName": "Your Application Name",
  "ResetPasswordBaseUrl": "https://[your-power-pages-domain]"
}
```

**Setup Steps**:

1. Create Azure Key Vault
2. Add SMTP2GO API key as secret
3. Configure Function App managed identity
4. Grant Key Vault access to Function App
5. Use Key Vault reference in app settings

### **Option 2: App Settings (Secure and Easy)**

**Best for**: Most production scenarios - secure and easy to manage

**Configuration**:

```json
{
  "SMTP2GOApiKey": "api-your-smtp2go-api-key-here",
  "SMTP2GOFromEmail": "<EMAIL>",
  "SMTP2GOFromName": "Your Application Name",
  "ResetPasswordBaseUrl": "https://[your-power-pages-domain]"
}
```

**Setup Steps**:

1. Go to Azure Portal → Function App → Configuration
2. Add application settings (automatically encrypted)
3. Values are secure and not visible in portal after saving

## **SMTP2GO Account Setup**

### **Domain Authentication**

**Why Required**: Without domain authentication, emails may go to spam

**Setup Steps**:

1. Go to SMTP2GO → Settings → Sender Authentication
2. Click "Authenticate Your Domain"
3. Enter your domain (e.g., `yourcompany.com`)
4. Add DNS records provided by SMTP2GO to your domain
5. Verify authentication (may take up to 48 hours)

### **Create API Key**

1. Go to SMTP2GO → Settings → API Keys
2. Click "Create API Key"
3. **Name**: "Power Pages Password Reset"
4. **Permissions**: "Send Email"
5. **Copy the API key** (starts with `api-`)

### **Verify Sender Email**

1. Go to SMTP2GO → Settings → Sender Authentication
2. Add and verify your "from" email address
3. Complete verification process

### **Test Email Delivery**

**Test Process**:

1. **Configure SMTP2GO settings** in Azure Function
2. **Deploy updated PasswordService** code
3. **Test forgot password flow**
4. **Verify email delivery**
5. **Complete password reset**
6. **Verify password changed notification**

## **Email Templates**

The system uses built-in HTML email templates with Osler branding:

### **Password Reset Email**
- Clean, professional design
- 6-digit verification code
- Clear instructions
- Osler color scheme (red, white, black)

### **Password Changed Notification**
- Confirmation of password change
- Security notice
- Contact information for issues

## **Monitoring**

### **SMTP2GO Dashboard**

Monitor email delivery in SMTP2GO dashboard:

1. Go to SMTP2GO → Reports
2. Check delivery statistics
3. Review bounce/spam reports
4. Monitor API usage

### **Application Insights Monitoring**

Azure Function logs include:

- Email send attempts
- Success/failure rates
- Error details with correlation IDs
- API response details

## **Email Service Features**

### **Bypass Mode**

When SMTP2GO API key is not configured:

- Service logs reset links to console
- Password flow continues normally
- Useful for development/testing

### **Error Handling**

- Comprehensive logging with correlation IDs
- Graceful fallback when email fails
- Detailed error reporting for troubleshooting

### **Security Features**

- API key validation
- Secure template rendering
- Input sanitization
- Rate limiting integration

## **Deployment Checklist**

### **Pre-Deployment**

- [ ] SMTP2GO account created and verified
- [ ] Domain authentication completed
- [ ] API key created with correct permissions
- [ ] Email templates tested and approved

### **Deployment**

- [ ] Configure SMTP2GO settings in Azure Function
- [ ] Deploy updated PasswordService code
- [ ] Test email delivery in production environment
- [ ] Verify monitoring and logging

### **Post-Deployment**

- [ ] Monitor email delivery rates
- [ ] Check for bounce/spam issues
- [ ] Verify all email flows work correctly
- [ ] Set up alerting for email failures

## **Troubleshooting**

### **Common Issues**

#### **API Key Format Error**
```
Expected format 'api-xxxxx' but got 'xxxxx'
```
**Solution**: Ensure API key starts with `api-`

#### **Email Delivery Failures**
**Solution**: Check domain authentication and sender verification

#### **Bypass Mode Active**
```
EMAIL BYPASS: Password reset email would be sent to...
```
**Solution**: Configure valid SMTP2GOApiKey

## **Microsoft Documentation References**

### **Azure Functions Email Integration**

- [Azure Functions HTTP triggers](https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-http-webhook)
- [Azure Functions app settings](https://learn.microsoft.com/en-us/azure/azure-functions/functions-app-settings)
- [Azure Key Vault integration](https://learn.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)

### **Email Security Best Practices**

- [Email authentication best practices](https://learn.microsoft.com/en-us/azure/communication-services/concepts/email/email-authentication-best-practice)
- [Email delivery and reliability](https://learn.microsoft.com/en-us/azure/communication-services/concepts/email/email-delivery-reliability)

### **SMTP2GO Documentation**

- [SMTP2GO API Documentation](https://developers.smtp2go.com/docs/introduction-guide)
- [SMTP2GO .NET SDK](https://github.com/smtp2go-oss/smtp2go-dotnet)
