Entra External ID setup, Power Pages configuration, and enhanced registration settings.

- **Standard Entra External ID authentication** for login (managed by Power Pages)
- **Unified password operations** for registration, forgot password, password reset, and password change (maintaining password history)
- **Enhanced user management** with application-specific data and context

## Entra External ID Configuration

### Access Entra External ID Admin Center

Configure Application Registration

1. **Create App Registration**

   - Click **New registration**
   - Name: "Power Pages Authentication"
   - Supported account types: **Accounts in this organizational directory only**

2. **Configure Authentication**

   - Platform: **Web**
   - Redirect URIs:
     ```
     https://[your-power-pages-domain]/.auth/login/EntraExternalID/callback
     https://[your-power-pages-domain]/.auth/login/aad/callback
     ```
   - Logout URL: `https://[your-power-pages-domain]/.auth/logout`

3. **Configure API Permissions**

   ```
   Microsoft Graph:
   - openid (Delegated)
   - email (Delegated)
   - profile (Delegated)
   - User.ReadWrite.All (Application) - For Azure Functions
   - Directory.ReadWrite.All (Application) - For Azure Functions
   ```

4. **Generate Client Secret**
   - Go to **Certificates & secrets**
   - Click **New client secret**
   - Description: "Power Pages Authentication"
   - **Copy the secret value** (you'll need this for Power Pages)

### Step 3: Configure User Flow

1. **Create User Flow**

   - Click **New user flow**
   - Select **Sign up and sign in**

1. **Example User Flow Configuration**

   ```
   Name: PP_1_signupsignin
   Identity providers: Email signup
   User attributes: Email Address, Display Name
   Application claims: Email Addresses, Display Name
   ```

1. **CRITICAL: Disable Self-Service Sign-up**

   See Microsoft document on making this patch to Graph API. This prevents users from bypassing our custom registration with password history validation.

### Step 4: Configure Extension Attributes

**Required Extension Attributes for Enhanced Registration**:

```
extension_ApplicationId          (String, Required)
extension_RegistrationDate       (String)
extension_RegistrationSource     (String)
extension_ApplicationName        (String)
extension_Department            (String)
extension_UserRole              (String)
extension_PasswordPolicyLevel   (String)
extension_LastPasswordHistoryCheck (String)
```

**How to Configure**:

1. Go to **App registrations** → Your app → **Token configuration**
2. Add optional claims for each extension attribute
3. Ensure attributes are available for user creation via Graph API

## Part 2: Power Pages Authentication Configuration

### Step 1: Access Power Pages Admin Center

1. Navigate to [Power Pages Admin Center](https://admin.powerplatform.microsoft.com/)
2. Select your Power Pages site
3. Go to **Set up** → **Authentication**

### Step 2: Configure Entra External ID Provider

1. **Add Identity Provider**

   - Add provider -> Entra External ID

2. **Provider Configuration**

   ```
   Provider Name: EntraExternalID
   Authority: https://[your-tenant-name].ciamlogin.com/[tenant-id]/v2.0/
   Client ID: [your-application-client-id]
   Client Secret: [your-application-client-secret]
   Scope: openid email profile
   ```

3. **Advanced Settings**
   ```
   Metadata Address: https://[your-tenant-name].ciamlogin.com/[tenant-id]/v2.0/.well-known/openid_configuration
   Response Type: code
   Response Mode: form_post
   ```

### Step 3: Configure Authentication Settings

1. **General Settings**

   ```
   Require Authentication: Yes
   Default Identity Provider: EntraExternalID
   Enable Registration: No (we'll use custom registration)
   ```

2. **Login/Logout URLs**

   ```
   Login URL: /.auth/login/EntraExternalID
   Logout URL: /.auth/logout
   ```

3. **Redirect URLs**
   ```
   Login Redirect: / (or your preferred landing page)
   Logout Redirect: /Custom-User-Login
   ```

## Part 3: Power Pages Site Configuration

### Step 1: Required Site Settings

Power Pages site (Setup → Site Settings):

#### **Core Settings (Required)**

```
AzureFunctionUrl = https://[your-function-app].azurewebsites.net/api
MSALClientId = [your-msal-client-id]
MSALTenantId = [your-tenant-id]
ApplicationId = [unique-application-identifier]
ApplicationName = [your-application-display-name]
```

#### **Authentication Settings**

```
Authentication/Registration/Enabled = false
Authentication/Registration/RequiresConfirmation = false
Authentication/Registration/RequiresInvitation = false
Authentication/Registration/RegistrationPath = /Custom-User-Registration

Authentication/PasswordReset/Enabled = false
Authentication/PasswordReset/ResetPasswordPath = /Custom-Password-Reset
Authentication/PasswordChange/Enabled = false
Authentication/PasswordChange/ChangePasswordPath = /Custom-Password-Reset

Authentication/LogoutPath = /.auth/logout
```

#### **Enhanced Settings (Optional but Recommended)**

```
ApplicationDescription = Secure customer portal for account management
SupportEmail = <EMAIL>
PrivacyPolicyUrl = https://yourcompany.com/privacy
TermsOfServiceUrl = https://yourcompany.com/terms
CompanyName = Your Company Name
```

### Step 2: Configure Custom Pages

Create pages and upload custom code for Registration, Forgot Password and Reset Password.

### Step 3: Configure Navigation

Update your site navigation - example

```
Login: Standard Power Pages Entra ID authentication (automatic)
Register: /Custom-User-Registration
Forgot Password: /Custom-Forgot-Password
Password Management: /Custom-Password-Reset (handles both reset and change)
```

## Part 4: Azure Functions Configuration

### Step 1: Application Settings

Configure these settings in your Azure Function App:

#### **Core Configuration**

```json
{
  "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=...",
  "EntraExternalID:TenantId": "your-tenant-id",
  "EntraExternalID:ClientId": "your-client-id",
  "EntraExternalID:ClientSecret": "your-client-secret",
  "EntraExternalID:DefaultDomain": "yourtenant.onmicrosoft.com",
  "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=..."
}
```

#### **Email Configuration (SMTP2GO)**

```json
{
  "SMTP2GOApiKey": "api-your-smtp2go-api-key",
  "SMTP2GOFromEmail": "<EMAIL>",
  "SMTP2GOFromName": "Your Application Name",
  "ResetPasswordBaseUrl": "https://[your-power-pages-domain]"
}
```

#### **Optional Configuration**

```json
{
  "RateLimit:MaxRequestsPerMinute": "60",
  "ApplicationName": "Your Application Name"
}
```

### Step 2: Secure Configuration Options

Azure Key Vault

```json
{
  "SMTP2GOApiKey": "@Microsoft.KeyVault(SecretUri=https://[vault].vault.azure.net/secrets/SMTP2GOApiKey/)",
  "CLIENT_SECRET": "@Microsoft.KeyVault(SecretUri=https://[vault].vault.azure.net/secrets/ClientSecret/)",
  "BLOB_CONNECTION_STRING": "@Microsoft.KeyVault(SecretUri=https://[vault].vault.azure.net/secrets/BlobConnectionString/)"
}
```

Alternate option - App Settings
Use the Azure Portal Application Settings interface to securely store sensitive values.

## Enhanced Registration Configuration

The enhanced registration system provides:

1. **Application-Specific User Data** - Users are properly associated with specific applications
2. **Registration Source Tracking** - Know how and when users registered
3. **Role-Based Context** - Support different user types and permissions
4. **Department Tracking** - Organizational context for users

### Required Configuration Changes

Add to Power Pages:

**Setting Name:** `ApplicationName`  
**Setting Value:** Your application's display name (e.g., "Customer Portal", "Employee Portal")

**How to Add**:

1. Power Pages Management Portal
2. Setup → Site Settings
3. Add:
   - Name: `ApplicationName`
   - Value: Your application's display name

**Example Values**:

```
ApplicationName = "Customer Portal"
ApplicationName = "Employee Self-Service Portal"
ApplicationName = "Partner Access Portal"
ApplicationName = "Vendor Management System"
```

### Benefits of Enhanced Configuration

#### **Enhanced User Management**

- Users clearly linked to specific applications
- Registration source tracking for audit purposes
- Role-based operations support
- Department tracking for organizational context

#### **Improved Reporting and Analytics**

- Application-specific metrics and user counts
- Registration source analysis
- User demographics and role distribution
- Password policy compliance tracking

#### **Better Security and Compliance**

- Application isolation prevents cross-application data access
- Comprehensive audit trail for user operations
- Role-based access control capabilities
- Password policy enforcement tracking

## Part 6: Testing and Validation

### Test 1: Standard Login Flow

1. Navigate to a protected page
2. Verify automatic redirect to standard Power Pages Entra ID authentication
3. Complete authentication with Entra External ID
4. Verify return to Power Pages as authenticated user

### Test 2: Enhanced Registration Flow

1. Navigate to `/Custom-User-Registration`
2. Fill out registration form with enhanced fields
3. Verify password history validation
4. Confirm user creation in Entra External ID with extension attributes
5. Test automatic redirect to login

### Test 3: Password Operations

1. **Forgot Password**:

   - Navigate to `/Custom-Forgot-Password`
   - Enter email address
   - Verify reset email delivery via SMTP2GO
   - Click reset link and complete password reset

2. **Password Management**:
   - **Password Reset**: Login as authenticated user and navigate to `/Custom-Password-Reset?mode=reset` (requires current password + logs out user)
   - **Password Change**: Login as authenticated user and navigate to `/Custom-Password-Reset` (requires current password + stays logged in)
   - Verify current password requirement for ALL password operations
   - Test password history validation for both operations
   - Verify logout behavior after password reset

### Test 4: Application Isolation

1. Register user in application A
2. Register same email in application B (if applicable)
3. Verify separate password histories
4. Confirm no cross-application access

## Part 7: Troubleshooting

### Common Issues

#### Issue 1: "Invalid redirect URI"

**Solution**: Ensure redirect URIs in Entra External ID match exactly:

```
https://[your-domain]/.auth/login/EntraExternalID/callback
```

#### Issue 2: Users can still self-register through Entra

**Solution**: Ensure user flow has self-service sign-up disabled:

```
Self-service sign-up: Disabled
```

#### Issue 3: Enhanced registration not working

**Solution**: Verify `ApplicationName` setting is configured in Power Pages:

```
Setting Name: ApplicationName
Setting Value: Your Application Display Name
```

#### Issue 4: Email delivery failures

**Solution**: Verify SMTP2GO configuration:

```
SMTP2GOApiKey = [valid-api-key]
SMTP2GOFromEmail = [verified-sender-email]
```

### Verification Steps

1. **Check Provider Configuration**

   ```
   Power Pages Admin → Set up → Authentication → Providers
   Verify EntraExternalID provider is configured and enabled
   ```

2. **Test Authentication URLs**

   ```
   Login URL: https://[your-domain]/.auth/login/EntraExternalID
   Should redirect to Entra External ID login page
   ```

3. **Verify Enhanced User Creation**

   ```
   Entra External ID Admin → Users
   Check that users have extension attributes populated
   ```

4. **Test Password History**
   ```
   Register user → Try to change to same password → Should be blocked
   ```

## Part 8: Security Considerations

### Authentication Security

1. **Client Secret Management**

   - Store client secret securely (Key Vault recommended)
   - Rotate secrets regularly (every 12-24 months)
   - Monitor for secret expiration

2. **Redirect URI Security**

   - Use HTTPS only for all redirect URIs
   - Validate redirect URIs match exactly
   - Avoid wildcard redirect URIs

3. **User Flow Security**
   - Disable self-service sign-up to enforce password history
   - Configure appropriate session timeouts
   - Enable conditional access if needed

### Custom Page Security

1. **CSRF Protection**

   - Include CSRF tokens in all custom forms
   - Validate requests on Azure Function side

2. **Input Validation**

   - Sanitize all user inputs
   - Implement rate limiting
   - Use secure password requirements

3. **Error Handling**
   - Don't expose sensitive information in error messages
   - Log security events for monitoring
   - Implement proper error pages

## Part 9: Maintenance and Monitoring

### Regular Tasks

1. **Monitor Authentication Logs**

   - Check Power Pages authentication logs
   - Review Entra External ID sign-in logs
   - Monitor Azure Function execution logs

2. **Update Configurations**

   - Keep client secrets current
   - Update redirect URIs if domain changes
   - Review and update user flow settings

3. **Test Functionality**
   - Regularly test complete authentication flows
   - Verify custom password operations
   - Check email delivery for password resets

### Security Reviews

1. **Quarterly Reviews**

   - Review authentication provider settings
   - Check for unused or expired secrets
   - Validate redirect URI configurations

2. **Annual Reviews**
   - Review user flow configurations
   - Update password policies if needed
   - Assess overall authentication security

## Conclusion

This complete configuration guide provides:

- **Comprehensive Setup** - All required configurations for the hybrid authentication system
- **Enhanced Features** - Application-specific user management and v1.0 API integration
- **Security Best Practices** - Secure configuration and maintenance procedures
- **Testing Procedures** - Validation steps to ensure proper functionality
- **Troubleshooting** - Common issues and solutions

Following this guide will result in a production-ready authentication system that combines the simplicity of standard Entra External ID authentication with the compliance requirements of custom password history management.

## Microsoft Documentation References

### Entra External ID Configuration

- [Microsoft Entra External ID overview](https://learn.microsoft.com/en-us/entra/external-id/external-identities-overview)
- [Configure Entra External ID for customers](https://learn.microsoft.com/en-us/entra/external-id/customers/overview-customers-ciam)
- [Create and configure user flows](https://learn.microsoft.com/en-us/entra/external-id/customers/how-to-user-flow-sign-up-sign-in-customers)
- [Configure custom attributes](https://learn.microsoft.com/en-us/entra/external-id/customers/how-to-define-custom-attributes)
- [Disable self-service sign-up](https://learn.microsoft.com/en-us/entra/external-id/customers/how-to-enable-self-service-sign-up)

### App Registration and Authentication

- [Register an application with Microsoft identity platform](https://learn.microsoft.com/en-us/entra/identity-platform/quickstart-register-app)
- [Configure authentication for a web app](https://learn.microsoft.com/en-us/entra/identity-platform/scenario-web-app-sign-user-overview)
- [Configure redirect URIs](https://learn.microsoft.com/en-us/entra/identity-platform/reply-url)
- [Add app roles to your application](https://learn.microsoft.com/en-us/entra/identity-platform/howto-add-app-roles-in-apps)
- [Configure API permissions](https://learn.microsoft.com/en-us/entra/identity-platform/quickstart-configure-app-access-web-apis)

### Power Pages Authentication

- [Power Pages authentication overview](https://learn.microsoft.com/en-us/power-pages/security/authentication/authentication-overview)
- [Configure authentication providers](https://learn.microsoft.com/en-us/power-pages/security/authentication/configure-authentication)
- [Set up OpenID Connect provider](https://learn.microsoft.com/en-us/power-pages/security/authentication/openid-settings)
- [Configure site settings](https://learn.microsoft.com/en-us/power-pages/configure/configure-site-settings)
- [Authentication site settings](https://learn.microsoft.com/en-us/power-pages/security/authentication/set-authentication-identity)

### Microsoft Graph API

- [Microsoft Graph overview](https://learn.microsoft.com/en-us/graph/overview)
- [Microsoft Graph v1.0 endpoint](https://learn.microsoft.com/en-us/graph/api/overview?view=graph-rest-1.0)
- [User resource type](https://learn.microsoft.com/en-us/graph/api/resources/user?view=graph-rest-1.0)
- [Create user](https://learn.microsoft.com/en-us/graph/api/user-post-users?view=graph-rest-1.0)
- [Update user](https://learn.microsoft.com/en-us/graph/api/user-update?view=graph-rest-1.0)
- [Directory extensions](https://learn.microsoft.com/en-us/graph/api/resources/extensionproperty?view=graph-rest-1.0)

### Azure Functions Configuration

- [Azure Functions overview](https://learn.microsoft.com/en-us/azure/azure-functions/functions-overview)
- [Azure Functions app settings](https://learn.microsoft.com/en-us/azure/azure-functions/functions-app-settings)
- [Manage function app](https://learn.microsoft.com/en-us/azure/azure-functions/functions-how-to-use-azure-function-app-settings)
- [Azure Functions security](https://learn.microsoft.com/en-us/azure/azure-functions/security-concepts)
- [Environment variables in Azure Functions](https://learn.microsoft.com/en-us/azure/azure-functions/functions-app-settings)

### Azure Key Vault Integration

- [Azure Key Vault overview](https://learn.microsoft.com/en-us/azure/key-vault/general/overview)
- [Use Key Vault references for App Service](https://learn.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)
- [Azure Key Vault security](https://learn.microsoft.com/en-us/azure/key-vault/general/security-features)
- [Best practices for Azure Key Vault](https://learn.microsoft.com/en-us/azure/key-vault/general/best-practices)

### SMTP2GO and Email Services

- [SMTP2GO API Documentation](https://developers.smtp2go.com/docs/introduction-guide)
- [Azure Communication Services Email](https://learn.microsoft.com/en-us/azure/communication-services/concepts/email/email-overview)
- [Email security best practices](https://learn.microsoft.com/en-us/azure/communication-services/concepts/email/email-authentication-best-practice)

### Security and Compliance

- [Microsoft identity platform security](https://learn.microsoft.com/en-us/entra/identity-platform/security-best-practices-for-app-registration)
- [OAuth 2.0 and OpenID Connect protocols](https://learn.microsoft.com/en-us/entra/identity-platform/v2-protocols)
- [Authentication flows and application scenarios](https://learn.microsoft.com/en-us/entra/identity-platform/authentication-flows-app-scenarios)
- [Conditional Access](https://learn.microsoft.com/en-us/entra/identity/conditional-access/overview)
- [Azure security baseline](https://learn.microsoft.com/en-us/security/benchmark/azure/baselines/azure-functions-security-baseline)
