using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace PasswordHistoryValidator.Shared;

public class RateLimitService : IDisposable
{
    private readonly ILogger<RateLimitService> _logger;
    private readonly RateLimitOptions _options;
    private readonly IMemoryCache _cache;
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _semaphores;
    private readonly Timer _cleanupTimer;

    public RateLimitService(ILogger<RateLimitService> logger, IOptions<RateLimitOptions> options, IMemoryCache cache)
    {
        _logger = logger;
        _options = options.Value;
        _cache = cache;
        _semaphores = new ConcurrentDictionary<string, SemaphoreSlim>();
        
        // Cleanup timer to remove old semaphores every 5 minutes
        _cleanupTimer = new Timer(CleanupOldSemaphores, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    public async Task<RateLimitInfo> CheckRateLimitAsync(string clientId, string operation, CancellationToken cancellationToken = default)
    {
        var key = $"rate_limit_{clientId}_{operation}";
        var semaphoreKey = $"semaphore_{key}";
        
        // Get or create semaphore for this key to ensure thread safety
        var semaphore = _semaphores.GetOrAdd(semaphoreKey, _ => new SemaphoreSlim(1, 1));
        
        await semaphore.WaitAsync(cancellationToken);
        try
        {
            var now = DateTime.UtcNow;
            
            if (_cache.TryGetValue(key, out RateLimitData? data))
            {
                // Check if window has expired
                if (now > data!.WindowEnd)
                {
                    // Reset window
                    data = new RateLimitData
                    {
                        Count = 1,
                        WindowStart = now,
                        WindowEnd = now.AddMinutes(1)
                    };
                    _cache.Set(key, data, TimeSpan.FromMinutes(1));
                    
                    return new RateLimitInfo
                    {
                        IsAllowed = true,
                        CurrentCount = 1,
                        MaxRequests = _options.MaxRequestsPerMinute,
                        WindowDuration = TimeSpan.FromMinutes(1),
                        WindowResetTime = data.WindowEnd
                    };
                }
                
                // Check if limit exceeded
                if (data.Count >= _options.MaxRequestsPerMinute)
                {
                    _logger.LogWarning("Rate limit exceeded for client {ClientId} operation {Operation}. Count: {Count}, Limit: {Limit}",
                        clientId, operation, data.Count, _options.MaxRequestsPerMinute);
                    
                    return new RateLimitInfo
                    {
                        IsAllowed = false,
                        CurrentCount = data.Count,
                        MaxRequests = _options.MaxRequestsPerMinute,
                        WindowDuration = TimeSpan.FromMinutes(1),
                        WindowResetTime = data.WindowEnd
                    };
                }
                
                // Increment count
                data.Count++;
                _cache.Set(key, data, data.WindowEnd - now);
                
                return new RateLimitInfo
                {
                    IsAllowed = true,
                    CurrentCount = data.Count,
                    MaxRequests = _options.MaxRequestsPerMinute,
                    WindowDuration = TimeSpan.FromMinutes(1),
                    WindowResetTime = data.WindowEnd
                };
            }
            
            // First request - create new window
            var newData = new RateLimitData
            {
                Count = 1,
                WindowStart = now,
                WindowEnd = now.AddMinutes(1)
            };
            _cache.Set(key, newData, TimeSpan.FromMinutes(1));
            
            return new RateLimitInfo
            {
                IsAllowed = true,
                CurrentCount = 1,
                MaxRequests = _options.MaxRequestsPerMinute,
                WindowDuration = TimeSpan.FromMinutes(1),
                WindowResetTime = newData.WindowEnd
            };
        }
        finally
        {
            semaphore.Release();
        }
    }

    public bool IsRequestAllowed(string clientId, string operation)
    {
        // Synchronous wrapper for backward compatibility
        return CheckRateLimitAsync(clientId, operation).GetAwaiter().GetResult().IsAllowed;
    }

    public RateLimitInfo GetRateLimitInfo(string clientId, string operation)
    {
        // Synchronous wrapper for backward compatibility
        return CheckRateLimitAsync(clientId, operation).GetAwaiter().GetResult();
    }

    public string GetHealthStatus()
    {
        try
        {
            // Simple health check - try to set and get a test value
            var testKey = $"health_check_{Guid.NewGuid()}";
            _cache.Set(testKey, "test", TimeSpan.FromSeconds(1));
            var retrieved = _cache.Get(testKey);
            return retrieved != null ? "healthy" : "degraded";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Rate limiter health check failed");
            return "unhealthy";
        }
    }

    private void CleanupOldSemaphores(object? state)
    {
        try
        {
            // Remove semaphores that haven't been used recently
            var keysToRemove = new List<string>();
            foreach (var kvp in _semaphores)
            {
                if (kvp.Value.CurrentCount == 1) // Not currently in use
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            foreach (var key in keysToRemove)
            {
                if (_semaphores.TryRemove(key, out var semaphore))
                {
                    semaphore.Dispose();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during semaphore cleanup");
        }
    }

    public void Dispose()
    {
        _cleanupTimer?.Dispose();
        
        foreach (var semaphore in _semaphores.Values)
        {
            semaphore.Dispose();
        }
        _semaphores.Clear();
    }
}

// Data structure for rate limiting
internal class RateLimitData
{
    public int Count { get; set; }
    public DateTime WindowStart { get; set; }
    public DateTime WindowEnd { get; set; }
}
