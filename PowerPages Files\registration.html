<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="https://alcdn.msauth.net/browser/2.38.0/js/msal-browser.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="power-pages-styles.css">
<link rel="stylesheet" href="registration.css">

    <meta name="azure-function-url" content="{{ settings['AzureFunctionUrl'] | default: '' }}">
    <meta name="registration-function-key" content="{{ settings['Registration Function Key'] | default: '' }}">
    <meta name="invitation-function-key" content="{{ settings['Invitation Function Key'] | default: '' }}">
    <meta name="authentication-function-key" content="{{ settings['Authentication Function Key'] | default: '' }}">
    <meta name="msal-client-id" content="{{ settings['MSALClientId'] | default: '' }}">
    <meta name="msal-tenant-id" content="{{ settings['MSALTenantId'] | default: '' }}">
    <meta name="application-name" content="{{ settings['ApplicationName'] | default: '' }}">
    <meta name="entra-tenant-domain" content="{{ settings['EntraTenantDomain'] | default: '' }}">

    <script>
      function getConfig() {
        const functionUrl = document.querySelector('meta[name="azure-function-url"]')?.content;
        const registrationFunctionKey = document.querySelector('meta[name="registration-function-key"]')?.content;
        const invitationFunctionKey = document.querySelector('meta[name="invitation-function-key"]')?.content;
        const authenticationFunctionKey = document.querySelector('meta[name="authentication-function-key"]')?.content;
        const msalClientId = document.querySelector('meta[name="msal-client-id"]')?.content;
        const msalTenantId = document.querySelector('meta[name="msal-tenant-id"]')?.content;
        const applicationName = document.querySelector('meta[name="application-name"]')?.content;
        const entraTenantDomain = document.querySelector('meta[name="entra-tenant-domain"]')?.content;

        return {
          functionUrl: functionUrl || null,
          registrationFunctionKey: registrationFunctionKey || null,
          invitationFunctionKey: invitationFunctionKey || null,
          authenticationFunctionKey: authenticationFunctionKey || null,
          msalClientId: msalClientId || null,
          msalTenantId: msalTenantId || null,
          applicationName: applicationName || null,
          entraTenantDomain: entraTenantDomain || null
        };
      }

      window.appConfig = getConfig();
    </script>

<!-- User Registration Form -->
<div class="container mt-5">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h3 class="text-center">Create Account</h3>
        </div>
        <div class="card-body">
          <!-- Registration Form -->
          <form id="registrationForm">
            <!-- Email Field -->
            <div class="form-group mb-3">
              <label for="email" class="form-label fw-bold">Email Address</label>
              <input
                type="email"
                id="email"
                required
                class="form-control"
                placeholder="Enter your email address"
              />
              <div class="invalid-feedback"></div>
            </div>

            <!-- First Name -->
            <div class="form-group mb-3">
              <label for="firstName" class="form-label fw-bold">First Name</label>
              <input
                type="text"
                id="firstName"
                required
                class="form-control"
                placeholder="Enter your first name"
              />
              <div class="invalid-feedback"></div>
            </div>

            <!-- Last Name -->
            <div class="form-group mb-3">
              <label for="lastName" class="form-label fw-bold">Last Name</label>
              <input
                type="text"
                id="lastName"
                required
                class="form-control"
                placeholder="Enter your last name"
              />
              <div class="invalid-feedback"></div>
            </div>

            <!-- Invitation Code -->
            <div class="form-group mb-3">
              <label for="invitationCode" class="form-label fw-bold">Invitation Code</label>
              <input
                type="text"
                id="invitationCode"
                required
                class="form-control"
                placeholder="Enter your invitation code"
                maxlength="20"
              />
              <div class="form-text">
                Enter the invitation code you received in your invitation email.
              </div>
              <div class="invalid-feedback"></div>
            </div>

            <!-- Password Field -->
            <div class="form-group mb-3">
              <label for="password" class="form-label fw-bold">Password</label>
              <div class="input-group">
                <input
                  type="password"
                  id="password"
                  required
                  class="form-control"
                  placeholder="Enter your password"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="togglePassword"
                  title="Toggle password visibility"
                  aria-label="Toggle password visibility"
                >
                  <i class="fas fa-eye"></i>
                </button>
              </div>
              <div class="form-text">
                Password must be at least 8 characters with uppercase,
                lowercase, number, and special character.
              </div>
              <div class="invalid-feedback"></div>
            </div>

            <!-- Confirm Password -->
            <div class="form-group mb-3">
              <label for="confirmPassword" class="form-label fw-bold">Confirm Password</label>
              <div class="input-group">
                <input
                  type="password"
                  id="confirmPassword"
                  required
                  class="form-control"
                  placeholder="Confirm your password"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="toggleConfirmPassword"
                  title="Toggle confirm password visibility"
                  aria-label="Toggle confirm password visibility"
                >
                  <i class="fas fa-eye"></i>
                </button>
              </div>
              <div class="invalid-feedback"></div>
            </div>

            <!-- Terms and Conditions -->
            <div class="form-check mb-3">
              <input
                type="checkbox"
                id="termsAccepted"
                required
                class="form-check-input"
              />
              <label for="termsAccepted" class="form-check-label">
                I agree to the
                <a href="/terms" target="_blank">Terms and Conditions</a>
              </label>
              <div class="invalid-feedback"></div>
            </div>

            <!-- Submit Button -->
            <button
              type="submit"
              id="registerButton"
              class="btn btn-primary w-100"
            >
              Create Account
            </button>
          </form>

          <!-- Messages -->
          <div id="errorMessage" class="alert alert-danger mt-3 d-none"></div>
          <div id="successMessage" class="alert alert-success mt-3 d-none"></div>

          <!-- Authentication Testing Section -->
          <div class="auth-testing-section mt-4">
            <h3>Authentication Testing</h3>
            <p class="text-muted">Test authentication functionality here or in the menu above.</p>

            <div class="auth-links-container">
              <div class="auth-link-group">
                <a href="https://entraexternaltestosler.ciamlogin.com/c0a19123-40d6-43e2-be53-aafde4c23d22/"
                   target="_blank"
                   class="btn btn-primary">
                  <i class="fas fa-sign-in-alt"></i> Sign In
                </a>
                <p class="auth-description">Test sign in functionality</p>
              </div>

              <div class="auth-link-group">
                <a href="/Account/Login/LogOff" class="btn btn-secondary">
                  <i class="fas fa-sign-out-alt"></i> Sign Out
                </a>
                <p class="auth-description">Sign out and return to homepage</p>
              </div>
            </div>
          </div>

          <!-- Login Link -->
          <div class="text-center mt-3">
            <p>Already have an account? <a href="/" class="btn btn-outline-primary">Sign in with your existing account</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Include Registration JavaScript -->
<script src="registration.js"></script>
