# CORS Security Issues - Known Issues

**Date**: 2025-01-31  
**Status**: TEMPORARY FIX APPLIED - SECURITY REVIEW REQUIRED  
**Priority**: HIGH - Address before production deployment

## Current CORS Configuration

### Issue Summary
The Azure Functions are currently configured with overly permissive CORS settings that allow requests from any origin (`"*"`). This was implemented as a temporary fix to resolve 401 Unauthorized errors during development.

### Current Configuration

**host.json**:
```json
{
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": ["*"],
        "allowedMethods": ["GET", "POST", "OPTIONS"],
        "allowedHeaders": [
          "Content-Type",
          "Authorization", 
          "x-functions-key",
          "X-Requested-With",
          "X-Client-Version"
        ]
      }
    }
  }
}
```

**BaseFunctionService.cs**:
```csharp
protected virtual void AddCorsHeaders(HttpResponseData response)
{
    response.Headers.Add("Access-Control-Allow-Origin", "*");
    response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization, x-functions-key, X-Requested-With, X-Client-Version");
    response.Headers.Add("Access-Control-Max-Age", "86400");
}
```

## Security Risks

### 1. **Open Origin Policy** ⚠️ **HIGH RISK**
- **Risk**: Any website can call your Azure Functions
- **Impact**: Potential for unauthorized access, data exposure, and abuse
- **Mitigation Needed**: Restrict to specific Power Pages domains

### 2. **Function Key Exposure** ⚠️ **MEDIUM RISK**
- **Risk**: Function keys visible in browser requests from any origin
- **Impact**: Keys could be harvested and misused
- **Mitigation Needed**: Domain restriction + key rotation policy

### 3. **Cross-Site Request Forgery (CSRF)** ⚠️ **MEDIUM RISK**
- **Risk**: Malicious sites could trigger requests to your functions
- **Impact**: Unauthorized operations on behalf of users
- **Mitigation Needed**: Origin validation + CSRF tokens

## Required Actions Before Production

### 1. **Identify Power Pages Domain**
Determine the actual Power Pages domain(s) that need access:
- Production domain: `https://[site-name].powerappsportals.com`
- Staging domain: `https://[staging-site].powerappsportals.com`
- Development domain: `https://[dev-site].powerappsportals.com`

### 2. **Update CORS Configuration**
Replace wildcard with specific domains:

**host.json**:
```json
{
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": [
          "https://your-production-site.powerappsportals.com",
          "https://your-staging-site.powerappsportals.com"
        ],
        "allowedMethods": ["GET", "POST", "OPTIONS"],
        "allowedHeaders": [
          "Content-Type",
          "Authorization",
          "x-functions-key", 
          "X-Requested-With",
          "X-Client-Version"
        ]
      }
    }
  }
}
```

**BaseFunctionService.cs**:
```csharp
protected virtual void AddCorsHeaders(HttpResponseData response)
{
    // Get allowed origins from configuration
    var allowedOrigins = Environment.GetEnvironmentVariable("CORS_ALLOWED_ORIGINS")?.Split(',') 
                        ?? new[] { "https://your-production-site.powerappsportals.com" };
    
    // For now, using wildcard - MUST BE CHANGED FOR PRODUCTION
    response.Headers.Add("Access-Control-Allow-Origin", "*");
    response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization, x-functions-key, X-Requested-With, X-Client-Version");
    response.Headers.Add("Access-Control-Max-Age", "86400");
}
```

### 3. **Implement Dynamic Origin Validation**
For better security, implement request-based origin validation:

```csharp
protected virtual void AddCorsHeaders(HttpResponseData response, HttpRequestData request)
{
    var origin = request.Headers.GetValues("Origin").FirstOrDefault();
    var allowedOrigins = GetAllowedOrigins(); // From config
    
    if (allowedOrigins.Contains(origin))
    {
        response.Headers.Add("Access-Control-Allow-Origin", origin);
    }
    
    response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization, x-functions-key, X-Requested-With, X-Client-Version");
    response.Headers.Add("Access-Control-Max-Age", "86400");
}
```

## Testing Requirements

### 1. **Verify CORS Restrictions**
- Test that requests from unauthorized domains are blocked
- Verify Power Pages can still access functions
- Check preflight OPTIONS requests work correctly

### 2. **Monitor Function Logs**
- Watch for CORS-related errors after restriction
- Monitor for unauthorized access attempts
- Verify legitimate requests continue working

## Timeline

- **Immediate**: Document current permissive configuration (✅ DONE)
- **Before Production**: Implement domain-specific CORS restrictions
- **Post-Production**: Regular security audits and key rotation

## Related Files

- `host.json` - Azure Functions CORS configuration
- `Shared/BaseFunctionService.cs` - Manual CORS header implementation
- `docs/PPAuth/Security Essentials.md` - General security guidelines

## Notes

This temporary fix resolves the immediate 401 Unauthorized errors but creates security vulnerabilities. The permissive CORS policy MUST be tightened before production deployment.
