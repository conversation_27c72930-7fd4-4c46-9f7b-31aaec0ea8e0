# Power Pages Function Authorization Setup

**CRITICAL**: This document outlines the **required** Power Pages configuration for Azure Function authorization. All steps must be completed for the system to work.

## 🚨 **Security Status**

✅ **Azure Functions**: All 5 functions now require function keys (AuthorizationLevel.Function)  
✅ **PowerPages JavaScript**: All files updated to support function keys  
⚠️ **Power Pages Settings**: **MUST BE CONFIGURED** (see below)

## 📋 **Required Power Pages Settings**

After deploying your Azure Functions, you **MUST** configure these Power Pages settings:

### **1. Get Your Function Key**

1. **Deploy** your Azure Functions via VS Code Azure Functions extension
2. **Go to Azure Portal** → Your Function App → Functions → Any function → Function Keys
3. **Copy the "default" key** (works for all functions)

### **2. Configure Power Pages Settings**

In your Power Pages admin portal, add these **exact** settings:

| Setting Name        | Setting Value                                 | Required |
| ------------------- | --------------------------------------------- | -------- |
| `AzureFunctionUrl`  | `https://your-function-app.azurewebsites.net` | ✅ YES   |
| `AzureFunctionKey`  | `[YOUR_FUNCTION_KEY_FROM_STEP_1]`             | ✅ YES   |
| `ApplicationName`   | `[YOUR_APP_NAME]`                             | ✅ YES   |
| `MSALClientId`      | `[YOUR_ENTRA_CLIENT_ID]`                      | ✅ YES   |
| `MSALTenantId`      | `[YOUR_ENTRA_TENANT_ID]`                      | ✅ YES   |
| `EntraTenantDomain` | `[YOUR_TENANT].ciamlogin.com`                 | ✅ YES   |

**⚠️ CRITICAL**: If any setting is missing, you'll see `ERROR_MISSING_[SettingName]` in browser console and API calls will fail.

## 🔧 **Updated PowerPages Files**

All PowerPages files have been updated to support function keys:

### **Files with Function Key Support**

- ✅ `registration.html` + `registration.js`
- ✅ `forgot-password.html` + `forgot-password.js`
- ✅ `reset-password.html` + `reset-password.js`
- ✅ `send-invitation.html` + `send-invitation.js`
- ✅ `invitation-error.html` (no API calls needed)

### **Simplified SecureConfig Pattern**

All JavaScript files now use this **single secure method**:

```javascript
const SecureConfig = {
  getFunctionKey() {
    const functionKey = window.appConfig?.azureFunctionKey;

    if (!functionKey || functionKey.includes("ERROR_MISSING")) {
      console.error(
        "Azure Function key not configured in Power Pages settings"
      );
      return null;
    }

    return functionKey;
  },

  buildSecureUrl(functionName, operation) {
    const baseUrl = this.getFunctionUrl(functionName);
    const functionKey = this.getFunctionKey();

    if (!baseUrl || !functionKey) {
      return null;
    }

    return `${baseUrl}?operation=${operation}&code=${functionKey}`;
  },
};

// Usage in API calls:
const secureUrl = SecureConfig.buildSecureUrl(
  "PasswordService",
  "reset-initiate"
);
if (!secureUrl) {
  throw new Error(
    "Azure Function configuration missing. Please check Power Pages settings."
  );
}

const response = await fetch(secureUrl, {
  method: "POST",
  // ... rest of request
});
```

**✅ Single Source of Truth**: Only `window.appConfig.azureFunctionKey` - no fallbacks, no complexity.

## 🔒 **CORS Configuration - CRITICAL SECURITY UPDATE**

### **⚠️ Security Issue Fixed**

**BEFORE** (Security Risk):

```json
"allowedOrigins": ["*"]  // ❌ Allows ANY website to call your APIs
```

**AFTER** (Secure):

```json
"allowedOrigins": ["https://your-powerpage.powerappsportals.com"]  // ✅ Only your Power Pages site
```

### **🎯 What You MUST Do**

**1. Find Your Power Pages URL:**

- Go to Power Pages admin portal
- Copy your site URL (e.g., `https://site-abc123.powerappsportals.com`)

**2. Update host.json:**

- Replace `https://your-powerpage.powerappsportals.com` with your actual URL
- **CRITICAL**: Must be exact match (including https://)

**3. Add Multiple Environments (if needed):**

```json
"allowedOrigins": [
  "https://your-dev-site.powerappsportals.com",
  "https://your-staging-site.powerappsportals.com",
  "https://your-prod-site.powerappsportals.com"
]
```

### **🚨 Why This Matters**

**Before Fix**: Any malicious website could call your Azure Functions
**After Fix**: Only your Power Pages site can access the APIs

## 🧪 **Testing the Setup**

### **1. Test Function Key Requirement**

**Without Function Key** (should fail):

```bash
curl https://your-function-app.azurewebsites.net/api/UtilityService?operation=health
# Expected: 401 Unauthorized
```

**With Function Key** (should work):

```bash
curl "https://your-function-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_FUNCTION_KEY"
# Expected: 200 OK with health status
```

### **2. Test Power Pages Configuration**

1. **Open any Power Pages page** (e.g., registration, forgot password)
2. **Open Browser Developer Tools** (F12)
3. **Check Console tab** for configuration errors:

**✅ Good Configuration:**

```javascript
// No ERROR_MISSING messages
// API calls succeed
```

**❌ Bad Configuration:**

```javascript
"ERROR_MISSING_AzureFunctionKey";
"Azure Function key not configured in Power Pages settings";
"401 Unauthorized";
```

## 🔒 **Security Benefits**

**Before** (Anonymous):

- ❌ Anyone could call your APIs directly
- ❌ No authentication required
- ❌ Public endpoints exposed

**After** (Function-level):

- ✅ Function key required for all API calls
- ✅ Only authorized Power Pages can access APIs
- ✅ Secure endpoint protection

## 🚨 **Troubleshooting**

### **"401 Unauthorized" Errors**

- ✅ Check `AzureFunctionKey` setting in Power Pages
- ✅ Verify function key is correct in Azure Portal
- ✅ Ensure all JavaScript files are updated

### **"Configuration Missing" Errors**

- ✅ Check all required Power Pages settings exist
- ✅ Verify setting names are **exact** (case-sensitive)
- ✅ Check browser console for specific missing settings

### **"CORS" Errors**

- ✅ Update `host.json` to allow your Power Pages domain
- ✅ Redeploy Azure Functions after CORS changes

## 📝 **Next Steps**

1. ✅ **Deploy** updated Azure Functions
2. ✅ **Get** function key from Azure Portal
3. ✅ **Configure** Power Pages settings (see table above)
4. ✅ **Test** API calls work with function keys
5. ✅ **Update** CORS settings in `host.json` (see Security Essentials doc)

**⚠️ IMPORTANT**: Do not proceed to production until all tests pass and configuration is verified.
