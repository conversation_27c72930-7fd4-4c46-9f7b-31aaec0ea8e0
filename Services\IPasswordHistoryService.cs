using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Services;

// Password history operations service
public interface IPasswordHistoryService
{
    // Legacy methods (single-tenant)
    Task<Result<List<string>>> GetPasswordHistoryAsync(string userId, CancellationToken cancellationToken = default);
    Task<Result<bool>> UpdatePasswordHistoryAsync(string userId, string newPassword, CancellationToken cancellationToken = default);
    Task<Result<bool>> ValidatePasswordAgainstHistoryAsync(string userId, string password, CancellationToken cancellationToken = default);

    // Application-aware methods (multi-tenant)
    Task<Result<List<string>>> GetPasswordHistoryAsync(string applicationId, string userId, CancellationToken cancellationToken = default);
    Task<Result<bool>> UpdatePasswordHistoryAsync(string applicationId, string userId, string newPassword, CancellationToken cancellationToken = default);
    Task<Result<bool>> ValidatePasswordAgainstHistoryAsync(string applicationId, string userId, string password, CancellationToken cancellationToken = default);
}
